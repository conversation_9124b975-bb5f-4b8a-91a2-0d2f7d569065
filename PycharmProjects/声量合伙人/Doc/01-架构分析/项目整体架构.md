# XXL-JOB 项目整体架构分析

## 1. 项目概述

XXL-JOB 是一个分布式任务调度框架，采用中心式设计，支持集群部署。

### 基本信息
- **项目名称**: XXL-JOB
- **版本**: 3.0.1-SNAPSHOT
- **许可证**: GPL v3
- **Java版本**: 17
- **构建工具**: Maven
- **作者**: xuxueli

## 2. 技术栈

### 核心框架
- **Spring Boot**: 3.4.2 - 应用框架
- **Spring Framework**: 6.2.2 - 核心容器
- **MyBatis**: 3.0.4 - 数据持久化
- **Netty**: 4.1.117 - 网络通信

### 数据库
- **MySQL**: 9.2.0 - 主数据库

### 工具库
- **Gson**: 2.12.1 - JSON处理
- **Groovy**: 4.0.25 - 动态脚本支持
- **SLF4J**: 2.0.16 - 日志门面
- **JUnit**: 5.11.4 - 单元测试

## 3. 模块架构

### 3.1 模块划分

```
xxl-job/
├── xxl-job-core/              # 核心库模块
├── xxl-job-admin/             # 调度中心模块  
└── xxl-job-executor-samples/  # 执行器示例模块
```

### 3.2 模块职责

#### xxl-job-core (核心库)
- **职责**: 提供核心API和通信协议
- **主要功能**:
  - 定义调度中心与执行器的通信接口
  - 提供执行器基础实现
  - 任务上下文管理
  - 日志处理
  - 网络通信

#### xxl-job-admin (调度中心)
- **职责**: 任务调度管理和Web界面
- **主要功能**:
  - 任务配置管理
  - 调度策略执行
  - 执行器注册管理
  - Web管理界面
  - 用户权限管理
  - 调度日志查看

#### xxl-job-executor-samples (执行器示例)
- **职责**: 提供执行器集成示例
- **主要功能**:
  - Spring Boot集成示例
  - 无框架集成示例
  - 最佳实践演示

## 4. 架构设计模式

### 4.1 整体架构模式
- **中心式架构**: 调度中心统一管理所有任务
- **分布式执行**: 执行器分布式部署，支持集群
- **注册中心模式**: 执行器主动注册到调度中心

### 4.2 通信架构
```
调度中心 (Admin)  <---> 执行器 (Executor)
     |                      |
     |-- HTTP/Netty --------|
     |-- 任务调度 ----------|
     |-- 心跳检测 ----------|
     |-- 日志收集 ----------|
```

### 4.3 核心设计模式
- **策略模式**: 路由策略、阻塞策略、失败策略
- **模板方法模式**: 任务执行流程
- **观察者模式**: 任务状态变更通知
- **工厂模式**: 执行器创建
- **代理模式**: 远程调用封装

## 5. 核心特性架构

### 5.1 调度策略
- **Cron表达式**: 基于时间的调度
- **固定频率**: 固定间隔执行
- **固定延迟**: 固定延迟执行
- **API触发**: 外部接口触发
- **父子任务**: 任务依赖调度

### 5.2 路由策略
- **第一个**: 选择第一个执行器
- **最后一个**: 选择最后一个执行器
- **轮询**: 依次轮询执行器
- **随机**: 随机选择执行器
- **一致性HASH**: 基于任务参数hash
- **最不经常使用**: LFU算法
- **最近最久未使用**: LRU算法
- **故障转移**: 自动切换到健康节点
- **忙碌转移**: 负载均衡
- **分片广播**: 广播到所有执行器

### 5.3 容错机制
- **失败重试**: 支持自定义重试次数
- **失败告警**: 邮件/短信/钉钉通知
- **超时控制**: 任务执行超时中断
- **阻塞处理**: 串行/丢弃/覆盖策略

## 6. 部署架构

### 6.1 单机部署
```
[调度中心] --> [MySQL数据库]
     |
     v
[执行器1] [执行器2] [执行器N]
```

### 6.2 集群部署
```
[负载均衡器]
     |
[调度中心1] [调度中心2] [调度中心N]
     |           |           |
     +-----[MySQL集群]-------+
     |
     v
[执行器集群1] [执行器集群2] [执行器集群N]
```

## 7. 数据流架构

### 7.1 任务调度流程
1. 调度中心扫描待执行任务
2. 根据路由策略选择执行器
3. 发送执行请求到执行器
4. 执行器执行任务并返回结果
5. 调度中心记录执行日志

### 7.2 执行器注册流程
1. 执行器启动时向调度中心注册
2. 定期发送心跳保持连接
3. 调度中心维护执行器列表
4. 执行器下线时自动移除

## 8. 扩展性设计

### 8.1 水平扩展
- 调度中心支持集群部署
- 执行器支持动态扩容
- 数据库支持读写分离

### 8.2 功能扩展
- 支持自定义路由策略
- 支持自定义告警方式
- 支持多种脚本语言
- 支持GLUE在线编程

## 9. 安全架构

### 9.1 通信安全
- 数据传输加密
- 访问令牌验证
- IP白名单控制

### 9.2 权限控制
- 用户角色管理
- 执行器权限控制
- 操作审计日志
