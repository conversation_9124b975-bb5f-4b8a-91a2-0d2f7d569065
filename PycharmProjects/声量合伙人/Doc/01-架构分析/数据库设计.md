# XXL-JOB 数据库设计分析

## 1. 数据库概述

XXL-JOB 使用 MySQL 数据库存储调度相关的所有数据，采用 utf8mb4 字符集，支持完整的Unicode字符。

### 数据库基本信息
- **数据库名**: xxl_job
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB

## 2. 核心表结构分析

### 2.1 xxl_job_info (任务信息表)

**表职责**: 存储任务的基本配置信息和调度策略

#### 核心字段分析
```sql
CREATE TABLE `xxl_job_info` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `job_group` int(11) NOT NULL COMMENT '执行器主键ID',
    `job_desc` varchar(255) NOT NULL COMMENT '任务描述',
    `author` varchar(64) DEFAULT NULL COMMENT '作者',
    `alarm_email` varchar(255) DEFAULT NULL COMMENT '报警邮件',
    
    -- 调度配置
    `schedule_type` varchar(50) NOT NULL DEFAULT 'NONE' COMMENT '调度类型',
    `schedule_conf` varchar(128) DEFAULT NULL COMMENT '调度配置',
    `misfire_strategy` varchar(50) NOT NULL DEFAULT 'DO_NOTHING' COMMENT '调度过期策略',
    
    -- 执行器配置
    `executor_route_strategy` varchar(50) DEFAULT NULL COMMENT '执行器路由策略',
    `executor_handler` varchar(255) DEFAULT NULL COMMENT '执行器任务handler',
    `executor_param` varchar(512) DEFAULT NULL COMMENT '执行器任务参数',
    `executor_block_strategy` varchar(50) DEFAULT NULL COMMENT '阻塞处理策略',
    `executor_timeout` int(11) NOT NULL DEFAULT '0' COMMENT '任务执行超时时间，单位秒',
    `executor_fail_retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '失败重试次数',
    
    -- GLUE配置
    `glue_type` varchar(50) NOT NULL COMMENT 'GLUE类型',
    `glue_source` mediumtext COMMENT 'GLUE源代码',
    `glue_remark` varchar(128) DEFAULT NULL COMMENT 'GLUE备注',
    `glue_updatetime` datetime DEFAULT NULL COMMENT 'GLUE更新时间',
    
    -- 任务关系
    `child_jobid` varchar(255) DEFAULT NULL COMMENT '子任务ID，多个逗号分隔',
    
    -- 调度状态
    `trigger_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '调度状态：0-停止，1-运行',
    `trigger_last_time` bigint(13) NOT NULL DEFAULT '0' COMMENT '上次调度时间',
    `trigger_next_time` bigint(13) NOT NULL DEFAULT '0' COMMENT '下次调度时间',
    
    PRIMARY KEY (`id`)
);
```

#### 关键枚举值
- **schedule_type**: NONE, CRON, FIX_RATE, FIX_DELAY
- **misfire_strategy**: DO_NOTHING, FIRE_ONCE_NOW
- **executor_route_strategy**: FIRST, LAST, ROUND, RANDOM, CONSISTENT_HASH, LEAST_FREQUENTLY_USED, LEAST_RECENTLY_USED, FAILOVER, BUSYOVER, SHARDING_BROADCAST
- **executor_block_strategy**: SERIAL_EXECUTION, DISCARD_LATER, COVER_EARLY
- **glue_type**: BEAN, GLUE_GROOVY, GLUE_SHELL, GLUE_PYTHON, GLUE_PHP, GLUE_NODEJS, GLUE_POWERSHELL

### 2.2 xxl_job_log (任务执行日志表)

**表职责**: 记录每次任务执行的详细日志信息

#### 核心字段分析
```sql
CREATE TABLE `xxl_job_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `job_group` int(11) NOT NULL COMMENT '执行器主键ID',
    `job_id` int(11) NOT NULL COMMENT '任务，主键ID',
    
    -- 执行器信息
    `executor_address` varchar(255) DEFAULT NULL COMMENT '执行器地址',
    `executor_handler` varchar(255) DEFAULT NULL COMMENT '执行器任务handler',
    `executor_param` varchar(512) DEFAULT NULL COMMENT '执行器任务参数',
    `executor_sharding_param` varchar(20) DEFAULT NULL COMMENT '执行器任务分片参数',
    `executor_fail_retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '失败重试次数',
    
    -- 调度信息
    `trigger_time` datetime DEFAULT NULL COMMENT '调度-时间',
    `trigger_code` int(11) NOT NULL COMMENT '调度-结果',
    `trigger_msg` text COMMENT '调度-日志',
    
    -- 执行信息
    `handle_time` datetime DEFAULT NULL COMMENT '执行-时间',
    `handle_code` int(11) NOT NULL COMMENT '执行-状态',
    `handle_msg` text COMMENT '执行-日志',
    
    -- 告警状态
    `alarm_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败',
    
    PRIMARY KEY (`id`),
    KEY `I_trigger_time` (`trigger_time`),
    KEY `I_handle_code` (`handle_code`),
    KEY `I_jobid_jobgroup` (`job_id`,`job_group`),
    KEY `I_job_id` (`job_id`)
);
```

#### 状态码定义
- **trigger_code**: 200-成功, 500-失败
- **handle_code**: 200-成功, 500-失败, 502-超时
- **alarm_status**: 0-默认, 1-无需告警, 2-告警成功, 3-告警失败

### 2.3 xxl_job_group (执行器组表)

**表职责**: 管理执行器组信息和地址配置

```sql
CREATE TABLE `xxl_job_group` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `app_name` varchar(64) NOT NULL COMMENT '执行器AppName',
    `title` varchar(12) NOT NULL COMMENT '执行器名称',
    `address_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '执行器地址类型：0=自动注册、1=手动录入',
    `address_list` text COMMENT '执行器地址列表，多地址逗号分隔',
    `update_time` datetime DEFAULT NULL,
    PRIMARY KEY (`id`)
);
```

### 2.4 xxl_job_registry (注册中心表)

**表职责**: 管理执行器的动态注册信息

```sql
CREATE TABLE `xxl_job_registry` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `registry_group` varchar(50) NOT NULL COMMENT '注册组',
    `registry_key` varchar(255) NOT NULL COMMENT '注册键',
    `registry_value` varchar(255) NOT NULL COMMENT '注册值',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `i_g_k_v` (`registry_group`, `registry_key`, `registry_value`)
);
```

### 2.5 xxl_job_user (用户表)

**表职责**: 管理系统用户和权限

```sql
CREATE TABLE `xxl_job_user` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL COMMENT '账号',
    `password` varchar(50) NOT NULL COMMENT '密码',
    `role` tinyint(4) NOT NULL COMMENT '角色：0-普通用户、1-管理员',
    `permission` varchar(255) DEFAULT NULL COMMENT '权限：执行器ID列表，多个逗号分割',
    PRIMARY KEY (`id`),
    UNIQUE KEY `i_username` (`username`)
);
```

### 2.6 辅助表

#### xxl_job_log_report (日志报表表)
```sql
CREATE TABLE `xxl_job_log_report` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `trigger_day` datetime DEFAULT NULL COMMENT '调度-时间',
    `running_count` int(11) NOT NULL DEFAULT '0' COMMENT '运行中-日志数量',
    `suc_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行成功-日志数量',
    `fail_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行失败-日志数量',
    `update_time` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `i_trigger_day` (`trigger_day`)
);
```

#### xxl_job_logglue (GLUE日志表)
```sql
CREATE TABLE `xxl_job_logglue` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `job_id` int(11) NOT NULL COMMENT '任务，主键ID',
    `glue_type` varchar(50) DEFAULT NULL COMMENT 'GLUE类型',
    `glue_source` mediumtext COMMENT 'GLUE源代码',
    `glue_remark` varchar(128) NOT NULL COMMENT 'GLUE备注',
    `add_time` datetime DEFAULT NULL,
    `update_time` datetime DEFAULT NULL,
    PRIMARY KEY (`id`)
);
```

#### xxl_job_lock (分布式锁表)
```sql
CREATE TABLE `xxl_job_lock` (
    `lock_name` varchar(50) NOT NULL COMMENT '锁名称',
    PRIMARY KEY (`lock_name`)
);
```

## 3. 表关系设计

### 3.1 核心关系图
```
xxl_job_group (1) -----> (N) xxl_job_info
xxl_job_info (1) ------> (N) xxl_job_log
xxl_job_info (1) ------> (N) xxl_job_logglue
xxl_job_user (1) ------> (N) xxl_job_group (通过permission字段)
```

### 3.2 关系说明
- **一对多关系**: 一个执行器组可以包含多个任务
- **一对多关系**: 一个任务可以有多条执行日志
- **多对多关系**: 用户与执行器组通过权限字段建立多对多关系

## 4. 状态机设计

### 4.1 任务状态机
```
[创建] -> [停止] -> [运行] -> [停止]
   |        |        |
   +--------+--------+
```

**状态转换**:
- 创建时默认为停止状态 (trigger_status = 0)
- 手动启动后变为运行状态 (trigger_status = 1)
- 可随时停止任务 (trigger_status = 0)

### 4.2 执行日志状态机
```
[调度中] -> [执行中] -> [成功/失败]
    |          |           |
    v          v           v
[调度失败] [执行超时]   [告警处理]
```

## 5. 索引设计分析

### 5.1 性能优化索引
- `xxl_job_log.I_trigger_time`: 按调度时间查询优化
- `xxl_job_log.I_handle_code`: 按执行状态查询优化
- `xxl_job_log.I_jobid_jobgroup`: 复合索引，任务日志查询优化
- `xxl_job_registry.i_g_k_v`: 唯一索引，防重复注册

### 5.2 查询模式分析
- **时间范围查询**: 日志按时间范围查询频繁
- **状态过滤查询**: 按执行状态过滤查询
- **任务关联查询**: 任务与日志的关联查询

## 6. 数据一致性设计

### 6.1 分布式锁机制
- 使用 `xxl_job_lock` 表实现分布式锁
- 保证集群环境下调度的一致性

### 6.2 事务设计
- 任务调度和日志记录在同一事务中
- 保证数据的一致性和完整性

## 7. 扩展性考虑

### 7.1 水平扩展
- 日志表可按时间分表
- 支持读写分离部署

### 7.2 数据归档
- 历史日志定期归档
- GLUE历史版本管理
