# XXL-JOB 业务流程和状态机分析

## 1. 整体业务架构

### 1.1 核心业务流程
```
任务配置 -> 任务调度 -> 执行器选择 -> 任务执行 -> 结果回调 -> 日志记录
```

### 1.2 主要参与者
- **调度中心**: 负责任务调度和管理
- **执行器**: 负责任务的具体执行
- **管理员**: 负责任务配置和监控

## 2. 任务调度核心流程

### 2.1 调度扫描流程
```mermaid
graph TD
    A[调度线程启动] --> B[获取分布式锁]
    B --> C[扫描待执行任务]
    C --> D{任务是否到期?}
    D -->|是| E[检查过期策略]
    D -->|否| F[加入时间轮]
    E --> G{过期策略}
    G -->|DO_NOTHING| H[忽略过期]
    G -->|FIRE_ONCE_NOW| I[立即执行一次]
    H --> J[计算下次执行时间]
    I --> J
    F --> J
    J --> K[更新任务状态]
    K --> L[释放锁]
    L --> M[等待下次扫描]
    M --> B
```

### 2.2 调度扫描详细步骤
1. **获取分布式锁**: 使用数据库行锁确保集群中只有一个节点执行调度
2. **预读任务**: 查询未来5秒内需要执行的任务
3. **时间轮处理**: 将任务按执行时间分配到时间轮中
4. **过期处理**: 处理已过期的任务
5. **状态更新**: 更新任务的下次执行时间

### 2.3 时间轮机制
```java
// 时间轮数据结构
Map<Integer, List<Integer>> ringData = new ConcurrentHashMap<>();
// Key: 秒数(0-59), Value: 该秒需要执行的任务ID列表
```

## 3. 任务触发流程

### 3.1 触发类型枚举
```java
public enum TriggerTypeEnum {
    MANUAL,     // 手动触发
    CRON,       // CRON表达式触发
    RETRY,      // 失败重试触发
    PARENT,     // 父任务触发
    API,        // API接口触发
    MISFIRE     // 过期补偿触发
}
```

### 3.2 任务触发流程
```mermaid
graph TD
    A[接收触发请求] --> B[参数验证]
    B --> C[获取任务信息]
    C --> D[获取执行器组]
    D --> E[执行器路由选择]
    E --> F[创建执行日志]
    F --> G[发送执行请求]
    G --> H{发送成功?}
    H -->|是| I[更新日志状态]
    H -->|否| J[记录失败日志]
    I --> K[等待执行结果]
    J --> L[触发失败告警]
```

### 3.3 路由策略选择
- **FIRST**: 选择第一个执行器
- **LAST**: 选择最后一个执行器
- **ROUND**: 轮询选择
- **RANDOM**: 随机选择
- **CONSISTENT_HASH**: 一致性哈希
- **LEAST_FREQUENTLY_USED**: 最少使用
- **LEAST_RECENTLY_USED**: 最近最少使用
- **FAILOVER**: 故障转移
- **BUSYOVER**: 忙碌转移
- **SHARDING_BROADCAST**: 分片广播

## 4. 执行器注册流程

### 4.1 执行器启动流程
```mermaid
graph TD
    A[执行器启动] --> B[初始化配置]
    B --> C[启动内嵌服务器]
    C --> D[向调度中心注册]
    D --> E{注册成功?}
    E -->|是| F[启动心跳线程]
    E -->|否| G[重试注册]
    F --> H[定期发送心跳]
    G --> D
    H --> I{心跳成功?}
    I -->|是| H
    I -->|否| J[重新注册]
    J --> D
```

### 4.2 注册信息结构
```java
public class RegistryParam {
    private String registryGroup;    // 注册组: "EXECUTOR"
    private String registryKey;      // 注册键: 执行器AppName
    private String registryValue;    // 注册值: 执行器地址
}
```

### 4.3 心跳机制
- **心跳间隔**: 30秒
- **超时时间**: 90秒
- **清理机制**: 定期清理超时的注册信息

## 5. 任务执行状态机

### 5.1 任务状态定义
```java
// 任务调度状态
trigger_status: 0-停止, 1-运行

// 执行日志状态
trigger_code: 200-调度成功, 500-调度失败
handle_code: 200-执行成功, 500-执行失败, 502-执行超时

// 告警状态
alarm_status: 0-默认, 1-无需告警, 2-告警成功, 3-告警失败
```

### 5.2 任务执行状态机
```mermaid
stateDiagram-v2
    [*] --> 已创建
    已创建 --> 已停止: 初始状态
    已停止 --> 运行中: 启动任务
    运行中 --> 已停止: 停止任务
    运行中 --> 调度中: 到达执行时间
    调度中 --> 执行中: 发送到执行器
    调度中 --> 调度失败: 发送失败
    执行中 --> 执行成功: 正常完成
    执行中 --> 执行失败: 执行异常
    执行中 --> 执行超时: 超时中断
    执行成功 --> 运行中: 等待下次调度
    执行失败 --> 重试中: 有重试次数
    执行失败 --> 运行中: 无重试次数
    执行超时 --> 运行中: 等待下次调度
    重试中 --> 执行中: 重新执行
    调度失败 --> 运行中: 等待下次调度
```

### 5.3 执行器任务状态机
```mermaid
stateDiagram-v2
    [*] --> 空闲
    空闲 --> 执行中: 接收任务
    执行中 --> 空闲: 执行完成
    执行中 --> 终止中: 接收终止信号
    终止中 --> 空闲: 终止完成
    执行中 --> 超时: 执行超时
    超时 --> 空闲: 强制终止
```

## 6. 异常处理机制

### 6.1 调度异常处理
```mermaid
graph TD
    A[调度异常] --> B{异常类型}
    B -->|网络异常| C[重试机制]
    B -->|执行器不可用| D[故障转移]
    B -->|任务配置错误| E[记录错误日志]
    C --> F{重试次数}
    F -->|未超限| G[延迟重试]
    F -->|已超限| H[标记失败]
    D --> I[选择其他执行器]
    G --> A
    H --> J[发送告警]
    I --> K[重新执行]
    E --> J
```

### 6.2 执行异常处理
- **超时处理**: 任务执行超时自动中断
- **重试机制**: 支持自定义重试次数
- **阻塞策略**: 
  - SERIAL_EXECUTION: 串行执行
  - DISCARD_LATER: 丢弃后续调度
  - COVER_EARLY: 覆盖之前调度

### 6.3 容错机制
- **故障转移**: 执行器故障时自动切换
- **负载均衡**: 避免单点过载
- **熔断机制**: 连续失败时暂停调度

## 7. 分片广播流程

### 7.1 分片广播机制
```mermaid
graph TD
    A[分片广播任务] --> B[获取执行器列表]
    B --> C[计算分片参数]
    C --> D[并发发送到所有执行器]
    D --> E[执行器1: 分片0/3]
    D --> F[执行器2: 分片1/3]
    D --> G[执行器3: 分片2/3]
    E --> H[处理分片数据]
    F --> I[处理分片数据]
    G --> J[处理分片数据]
    H --> K[返回执行结果]
    I --> K
    J --> K
    K --> L[汇总分片结果]
```

### 7.2 分片参数
```java
// 分片参数格式: index/total
// 例如: 0/3, 1/3, 2/3
String shardingParam = broadcastIndex + "/" + broadcastTotal;
```

## 8. 子任务依赖流程

### 8.1 父子任务关系
```mermaid
graph TD
    A[父任务执行成功] --> B[检查子任务配置]
    B --> C{有子任务?}
    C -->|是| D[解析子任务ID列表]
    C -->|否| E[流程结束]
    D --> F[遍历子任务]
    F --> G[触发子任务执行]
    G --> H{还有子任务?}
    H -->|是| F
    H -->|否| E
```

### 8.2 子任务配置
```java
// 子任务ID配置，多个用逗号分隔
child_jobid: "2,3,4"
```

## 9. 日志处理流程

### 9.1 日志生命周期
```mermaid
graph TD
    A[创建执行日志] --> B[记录调度信息]
    B --> C[发送执行请求]
    C --> D[执行器处理]
    D --> E[实时日志写入]
    E --> F[执行完成回调]
    F --> G[更新执行结果]
    G --> H[日志归档]
    H --> I[定期清理]
```

### 9.2 日志清理策略
- **保留天数**: 可配置日志保留天数
- **自动清理**: 定时任务自动清理过期日志
- **手动清理**: 支持手动清理指定时间范围的日志

## 10. 监控告警流程

### 10.1 告警触发条件
- 任务执行失败
- 任务执行超时
- 执行器连接失败
- 调度异常

### 10.2 告警处理流程
```mermaid
graph TD
    A[告警触发] --> B[检查告警配置]
    B --> C{需要告警?}
    C -->|是| D[发送告警通知]
    C -->|否| E[更新告警状态]
    D --> F{发送成功?}
    F -->|是| G[标记告警成功]
    F -->|否| H[标记告警失败]
    G --> I[记录告警日志]
    H --> I
    E --> I
```

### 10.3 告警方式
- **邮件告警**: 支持多邮箱群发
- **短信告警**: 可扩展短信接口
- **钉钉告警**: 可扩展钉钉机器人
- **自定义告警**: 支持自定义告警方式
