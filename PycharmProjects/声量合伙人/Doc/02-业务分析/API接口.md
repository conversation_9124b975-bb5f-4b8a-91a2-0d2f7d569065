# XXL-JOB API接口分析

## 1. API架构设计

### 1.1 接口分类
XXL-JOB 的API接口主要分为两大类：
- **Web管理接口**: 供前端页面调用的HTTP接口
- **内部通信接口**: 调度中心与执行器之间的通信接口

### 1.2 通信协议
- **HTTP协议**: 基于RESTful风格的HTTP接口
- **JSON格式**: 统一使用JSON进行数据交换
- **POST方法**: 主要使用POST方法进行数据传输

## 2. 统一返回格式

### 2.1 ReturnT 通用返回对象
```java
public class ReturnT<T> {
    private int code;        // 状态码
    private String msg;      // 消息
    private T content;       // 数据内容
}
```

### 2.2 状态码规范
```java
public static final int SUCCESS_CODE = 200;  // 成功
public static final int FAIL_CODE = 500;     // 失败
```

### 2.3 返回示例
```json
{
    "code": 200,
    "msg": "操作成功",
    "content": {
        "id": 1,
        "name": "示例数据"
    }
}
```

## 3. Web管理接口

### 3.1 任务管理接口 (/jobinfo)

#### 3.1.1 任务列表查询
```
POST /jobinfo/pageList
```

**请求参数**:
```json
{
    "start": 0,              // 起始位置
    "length": 10,            // 页面大小
    "jobGroup": 1,           // 执行器组ID
    "triggerStatus": -1,     // 触发状态 (-1:全部, 0:停止, 1:运行)
    "jobDesc": "",           // 任务描述
    "executorHandler": "",   // 执行器Handler
    "author": ""             // 作者
}
```

**返回数据**:
```json
{
    "code": 200,
    "msg": null,
    "content": {
        "recordsTotal": 100,
        "recordsFiltered": 10,
        "data": [
            {
                "id": 1,
                "jobGroup": 1,
                "jobDesc": "测试任务",
                "scheduleType": "CRON",
                "scheduleConf": "0 0 0 * * ?",
                "triggerStatus": 1,
                "triggerLastTime": 1640995200000,
                "triggerNextTime": 1641081600000
            }
        ]
    }
}
```

#### 3.1.2 新增任务
```
POST /jobinfo/add
```

**请求参数**:
```json
{
    "jobGroup": 1,                          // 执行器组ID
    "jobDesc": "测试任务",                   // 任务描述
    "author": "admin",                      // 作者
    "alarmEmail": "<EMAIL>",      // 报警邮件
    "scheduleType": "CRON",                 // 调度类型
    "scheduleConf": "0 0 0 * * ?",         // 调度配置
    "misfireStrategy": "DO_NOTHING",        // 调度过期策略
    "executorRouteStrategy": "FIRST",       // 执行器路由策略
    "executorHandler": "demoJobHandler",    // 执行器Handler
    "executorParam": "",                    // 执行器参数
    "executorBlockStrategy": "SERIAL_EXECUTION", // 阻塞处理策略
    "executorTimeout": 0,                   // 任务超时时间
    "executorFailRetryCount": 0,            // 失败重试次数
    "glueType": "BEAN",                     // GLUE类型
    "childJobId": ""                        // 子任务ID
}
```

#### 3.1.3 更新任务
```
POST /jobinfo/update
```

#### 3.1.4 删除任务
```
POST /jobinfo/remove
```

#### 3.1.5 启动任务
```
POST /jobinfo/start
```

#### 3.1.6 停止任务
```
POST /jobinfo/stop
```

#### 3.1.7 触发任务
```
POST /jobinfo/trigger
```

### 3.2 执行器管理接口 (/jobgroup)

#### 3.2.1 执行器列表
```
POST /jobgroup/pageList
```

#### 3.2.2 新增执行器
```
POST /jobgroup/save
```

**请求参数**:
```json
{
    "appname": "xxl-job-executor-sample",   // 执行器AppName
    "title": "示例执行器",                   // 执行器名称
    "addressType": 0,                       // 地址类型 (0:自动注册, 1:手动录入)
    "addressList": ""                       // 地址列表
}
```

### 3.3 调度日志接口 (/joblog)

#### 3.3.1 日志列表查询
```
POST /joblog/pageList
```

**请求参数**:
```json
{
    "start": 0,
    "length": 10,
    "jobGroup": 1,
    "jobId": 1,
    "logStatus": 1,                         // 日志状态
    "filterTime": "2024-01-01 - 2024-01-31" // 时间范围
}
```

#### 3.3.2 日志详情
```
POST /joblog/logDetailCat
```

#### 3.3.3 终止任务
```
POST /joblog/logKill
```

#### 3.3.4 清理日志
```
POST /joblog/clearLog
```

### 3.4 用户管理接口 (/user)

#### 3.4.1 用户列表
```
POST /user/pageList
```

#### 3.4.2 新增用户
```
POST /user/add
```

#### 3.4.3 更新用户
```
POST /user/update
```

#### 3.4.4 删除用户
```
POST /user/remove
```

## 4. 内部通信接口

### 4.1 调度中心API接口 (/api)

#### 4.1.1 执行器回调
```
POST /api/callback
```

**请求参数**:
```json
[
    {
        "logId": 1,                         // 日志ID
        "logDateTim": 1640995200000,        // 日志时间
        "handleCode": 200,                  // 处理状态码
        "handleMsg": "执行成功"              // 处理消息
    }
]
```

#### 4.1.2 执行器注册
```
POST /api/registry
```

**请求参数**:
```json
{
    "registryGroup": "EXECUTOR",            // 注册组
    "registryKey": "xxl-job-executor-sample", // 注册键
    "registryValue": "http://127.0.0.1:9999/" // 注册值
}
```

#### 4.1.3 执行器注销
```
POST /api/registryRemove
```

### 4.2 执行器API接口

#### 4.2.1 心跳检测
```
POST /beat
```

#### 4.2.2 空闲心跳
```
POST /idleBeat
```

**请求参数**:
```json
{
    "jobId": 1                              // 任务ID
}
```

#### 4.2.3 任务执行
```
POST /run
```

**请求参数**:
```json
{
    "jobId": 1,                             // 任务ID
    "executorHandler": "demoJobHandler",    // 执行器Handler
    "executorParams": "",                   // 执行器参数
    "executorBlockStrategy": "SERIAL_EXECUTION", // 阻塞策略
    "executorTimeout": 0,                   // 超时时间
    "logId": 1,                             // 日志ID
    "logDateTime": 1640995200000,           // 日志时间
    "glueType": "BEAN",                     // GLUE类型
    "glueSource": "",                       // GLUE源码
    "glueUpdatetime": 1640995200000,        // GLUE更新时间
    "broadcastIndex": 0,                    // 分片索引
    "broadcastTotal": 1                     // 分片总数
}
```

#### 4.2.4 任务终止
```
POST /kill
```

**请求参数**:
```json
{
    "jobId": 1                              // 任务ID
}
```

#### 4.2.5 日志查询
```
POST /log
```

**请求参数**:
```json
{
    "logDateTim": 1640995200000,            // 日志时间
    "logId": 1,                             // 日志ID
    "fromLineNum": 0                        // 起始行号
}
```

## 5. 安全机制

### 5.1 访问令牌验证
```java
// 请求头中携带访问令牌
XXL-JOB-ACCESS-TOKEN: your-access-token
```

### 5.2 权限控制
- **管理员权限**: 拥有所有操作权限
- **普通用户权限**: 只能操作分配的执行器

### 5.3 参数验证
- 必填参数验证
- 参数格式验证
- 参数长度限制
- SQL注入防护

## 6. 错误处理

### 6.1 常见错误码
```java
200 - 操作成功
500 - 操作失败
501 - 参数错误
502 - 超时
503 - 系统繁忙
```

### 6.2 错误信息格式
```json
{
    "code": 500,
    "msg": "任务不存在",
    "content": null
}
```

## 7. 接口版本控制

### 7.1 版本策略
- 当前版本: v3.0.1
- 向后兼容原则
- 废弃接口渐进式下线

### 7.2 版本标识
- URL路径版本: `/api/v1/`
- 请求头版本: `API-Version: v1`

## 8. 性能优化

### 8.1 接口优化
- 分页查询优化
- 批量操作支持
- 异步处理机制
- 缓存策略

### 8.2 限流机制
- 接口调用频率限制
- 并发请求数限制
- 熔断降级机制

## 9. 监控和日志

### 9.1 接口监控
- 调用次数统计
- 响应时间监控
- 错误率统计
- 性能指标收集

### 9.2 日志记录
- 请求日志记录
- 错误日志记录
- 性能日志记录
- 审计日志记录
